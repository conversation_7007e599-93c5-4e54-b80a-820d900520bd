{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:08:57","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:08:57","url":"/unknown-route"}
{"contentLength":"599","duration":"54ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:10:24","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:10:24","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 14:11:11"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 14:11:11"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:11:55","url":"/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"113","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:11:55","url":"/"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:11:55 +0000] \"GET / HTTP/1.1\" 200 113 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:11:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:12:09","url":"/health/live","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:12:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:12:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:12:09"}
{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:15:47","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:15:47","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 14:18:09"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 14:18:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:18:45","url":"/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"113","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:18:45","url":"/"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:18:45 +0000] \"GET / HTTP/1.1\" 200 113 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:18:45"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:20:51","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:20:51","url":"/unknown-route"}
{"contentLength":"599","duration":"82ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:22:04","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:22:04","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:25:52","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:25:52","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 17:30:51"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 17:30:51"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:31:16","url":"/auth/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"85","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 17:31:16","url":"/auth/"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:31:16 +0000] \"GET /auth/ HTTP/1.1\" 404 85 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 17:31:16"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"50ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-15 17:31:26","totalServices":4}
{"contentLength":"604","duration":"52ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:31:26","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:31:26 +0000] \"GET /health HTTP/1.1\" 503 604 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 17:31:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"/health","userAgent":"axios/1.10.0"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"136ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-15 17:32:46","totalServices":4}
{"contentLength":"606","duration":"136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:32:46","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:32:46 +0000] \"GET /health HTTP/1.1\" 503 606 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:32:46"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"/health","userAgent":"axios/1.10.0"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"33ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-15 17:33:26","totalServices":4}
{"contentLength":"605","duration":"35ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:26","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:33:26 +0000] \"GET /health HTTP/1.1\" 503 605 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:33:26"}
{"contentLength":"57","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 17:33:26"}
{"contentLength":"1470","duration":"131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:33:27 +0000] \"POST /auth/register HTTP/1.1\" 503 1470 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:33:27"}
{"contentLength":"42","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 17:33:27","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 17:33:27"}
{"contentLength":"238","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:33:27 +0000] \"POST /auth/register HTTP/1.1\" 400 238 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:33:27"}
