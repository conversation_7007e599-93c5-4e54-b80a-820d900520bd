const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

console.log('Starting minimal notification service...');

// Create Express app
const app = express();
const server = http.createServer(app);

// Socket.IO setup
const io = socketIo(server, {
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true
  }
});

// Basic middleware
app.use(express.json());

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'ATMA Notification Service (Minimal) is running',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Health endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    websocket: {
      status: 'running',
      connectedClients: Object.keys(io.sockets.sockets).length
    }
  });
});

// WebSocket Connection Handling
io.on('connection', (socket) => {
  console.log('WebSocket connection established:', socket.id);

  // Send welcome message
  socket.emit('connected', {
    message: 'Connected to notification service',
    timestamp: new Date().toISOString(),
    socketId: socket.id
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log('WebSocket connection disconnected:', socket.id, reason);
  });
});

const PORT = process.env.PORT || 3005;

// Start server
server.listen(PORT, () => {
  console.log(`Minimal Notification Service started on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});
