const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'atma_db',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  dialect: process.env.DB_DIALECT || 'postgres',
  schema: process.env.DB_SCHEMA || 'auth',
  logging: process.env.NODE_ENV === 'development' ? 
    (msg) => logger.debug(msg) : false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    schema: process.env.DB_SCHEMA || 'auth'
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(config);

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('Database connection established successfully', {
      host: config.host,
      database: config.database,
      schema: config.schema
    });
    return true;
  } catch (error) {
    logger.error('Unable to connect to the database', {
      error: error.message,
      host: config.host,
      database: config.database
    });
    return false;
  }
};

// Initialize database connection but don't throw error
// This allows the service to start even if the database is not available
testConnection();

module.exports = sequelize;
module.exports.testConnection = testConnection;
