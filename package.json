{"name": "atma-backend", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend Microservices", "main": "index.js", "scripts": {"install:all": "npm run install:gateway && npm run install:auth && npm run install:archive && npm run install:assessment && npm run install:worker && npm run install:notification", "install:gateway": "cd api-gateway && npm install", "install:auth": "cd auth-service && npm install", "install:archive": "cd archive-service && npm install", "install:assessment": "cd assessment-service && npm install", "install:worker": "cd analysis-worker && npm install", "install:notification": "cd notification-service && npm install", "dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:archive\" \"npm run dev:assessment\" \"npm run dev:worker\" \"npm run dev:notification\"", "dev:gateway": "cd api-gateway && npm run dev", "dev:auth": "cd auth-service && npm run dev", "dev:archive": "cd archive-service && npm run dev", "dev:assessment": "cd assessment-service && npm run dev", "dev:worker": "cd analysis-worker && npm run dev", "dev:notification": "cd notification-service && npm run dev", "start": "concurrently \"npm run start:gateway\" \"npm run start:auth\" \"npm run start:archive\" \"npm run start:assessment\" \"npm run start:worker\" \"npm run start:notification\"", "start:gateway": "cd api-gateway && npm start", "start:auth": "cd auth-service && npm start", "start:archive": "cd archive-service && npm start", "start:assessment": "cd assessment-service && npm start", "start:worker": "cd analysis-worker && npm start", "start:notification": "cd notification-service && npm start", "test": "npm run test:auth && npm run test:archive && npm run test:assessment && npm run test:worker && npm run test:notification", "test:auth": "cd auth-service && npm test", "test:archive": "cd archive-service && npm test", "test:assessment": "cd assessment-service && npm test", "test:worker": "cd analysis-worker && npm test", "test:notification": "cd notification-service && npm test", "migrate": "npm run migrate:auth && npm run migrate:archive", "migrate:auth": "cd auth-service && npm run migrate", "migrate:archive": "cd archive-service && npm run migrate", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "infrastructure:up": "docker-compose up -d postgres rabbitmq", "infrastructure:down": "docker-compose down postgres rabbitmq", "lint": "npm run lint:gateway && npm run lint:auth && npm run lint:archive && npm run lint:assessment && npm run lint:worker && npm run lint:notification", "lint:gateway": "cd api-gateway && npm run lint", "lint:auth": "cd auth-service && npm run lint", "lint:archive": "cd archive-service && npm run lint", "lint:assessment": "cd assessment-service && npm run lint", "lint:worker": "cd analysis-worker && npm run lint", "lint:notification": "cd notification-service && npm run lint", "health": "curl -f http://localhost:3000/health && curl -f http://localhost:3001/health && curl -f http://localhost:3002/health && curl -f http://localhost:3003/health && curl -f http://localhost:3005/health"}, "devDependencies": {"concurrently": "^8.0.1"}, "keywords": ["microservices", "nodejs", "express", "postgresql", "rabbitmq", "ai", "assessment", "talent-mapping"], "author": "ATMA Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"amqplib": "^0.10.8", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "socket.io": "^4.8.1", "winston": "^3.17.0"}}