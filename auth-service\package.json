{"name": "atma-auth-service", "version": "1.0.0", "description": "Authentication Service for ATMA Backend", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "health": "node scripts/health-check.js"}, "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "helmet": "^6.1.5", "joi": "^17.9.1", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "pg": "^8.10.0", "sequelize": "^6.31.0", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"axios": "^1.10.0", "eslint": "^8.39.0", "jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "keywords": ["authentication", "jwt", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}