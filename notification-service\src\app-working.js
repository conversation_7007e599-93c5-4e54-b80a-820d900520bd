const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const jwt = require('jsonwebtoken');
require('dotenv').config();

console.log('Starting ATMA Notification Service...');

// Create Express app
const app = express();
const server = http.createServer(app);

// Socket.IO setup
const io = socketIo(server, {
  cors: {
    origin: process.env.WEBSOCKET_CORS_ORIGIN ? 
      process.env.WEBSOCKET_CORS_ORIGIN.split(',') : 
      ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST']
  },
  pingTimeout: parseInt(process.env.WEBSOCKET_PING_TIMEOUT || '60000'),
  pingInterval: parseInt(process.env.WEBSOCKET_PING_INTERVAL || '25000')
});

const PORT = process.env.PORT || 3005;

// CORS configuration for HTTP routes
const corsOptions = {
  origin: process.env.CORS_ORIGIN ? 
    process.env.CORS_ORIGIN.split(',') : 
    ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request ID middleware
app.use((req, res, next) => {
  req.id = require('crypto').randomUUID();
  res.setHeader('X-Request-ID', req.id);
  next();
});

// Simple connection tracking
const userConnections = new Map();

// WebSocket Authentication Middleware
io.use((socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    
    if (!token) {
      console.warn('WebSocket authentication failed: No token provided');
      return next(new Error('Authentication token required'));
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Attach user info to socket
    socket.userId = decoded.id;
    socket.userEmail = decoded.email;
    
    console.log('WebSocket authentication successful:', decoded.id);
    next();
    
  } catch (error) {
    console.warn('WebSocket authentication failed:', error.message);
    next(new Error('Authentication failed'));
  }
});

// WebSocket Connection Handling
io.on('connection', (socket) => {
  console.log('WebSocket connection established:', socket.id, 'User:', socket.userId);

  // Add connection to tracking
  if (!userConnections.has(socket.userId)) {
    userConnections.set(socket.userId, new Set());
  }
  userConnections.get(socket.userId).add(socket);

  // Send welcome message
  socket.emit('connected', {
    message: 'Connected to notification service',
    timestamp: new Date().toISOString(),
    socketId: socket.id
  });

  // Handle ping for keeping connection alive
  socket.on('ping', () => {
    socket.emit('pong', { timestamp: new Date().toISOString() });
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log('WebSocket connection disconnected:', socket.id, reason);
    
    // Remove connection from tracking
    if (userConnections.has(socket.userId)) {
      userConnections.get(socket.userId).delete(socket);
      if (userConnections.get(socket.userId).size === 0) {
        userConnections.delete(socket.userId);
      }
    }
  });

  // Handle connection errors
  socket.on('error', (error) => {
    console.error('WebSocket connection error:', socket.id, error.message);
  });
});

// Make io available to other modules
app.set('io', io);

// Internal service authentication middleware
const authenticateInternalService = (req, res, next) => {
  const isInternalService = req.headers['x-internal-service'];
  const serviceKey = req.headers['x-service-key'];

  if (!isInternalService || isInternalService !== 'true') {
    return res.status(401).json({
      success: false,
      error: { code: 'UNAUTHORIZED', message: 'Internal service authentication required' }
    });
  }

  if (!serviceKey || serviceKey !== process.env.INTERNAL_SERVICE_KEY) {
    return res.status(401).json({
      success: false,
      error: { code: 'UNAUTHORIZED', message: 'Invalid service key' }
    });
  }

  next();
};

// Helper function to send notification to user
const sendNotificationToUser = (userId, eventType, data) => {
  const userSockets = userConnections.get(userId);
  if (!userSockets || userSockets.size === 0) {
    return { success: false, delivered: false, connectedClients: 0 };
  }

  const notification = {
    type: eventType,
    timestamp: new Date().toISOString(),
    ...data
  };

  let deliveredCount = 0;
  for (const socket of userSockets) {
    try {
      socket.emit(eventType, notification);
      deliveredCount++;
    } catch (error) {
      console.error('Failed to send notification to socket:', socket.id, error.message);
    }
  }

  return {
    success: deliveredCount > 0,
    delivered: deliveredCount > 0,
    connectedClients: userSockets.size,
    deliveredCount
  };
};

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    websocket: {
      status: 'running',
      connectedUsers: userConnections.size,
      totalConnections: Array.from(userConnections.values()).reduce((total, sockets) => total + sockets.size, 0)
    },
    version: '1.0.0'
  });
});

// Analysis complete notification
app.post('/notifications/analysis-complete', authenticateInternalService, (req, res) => {
  try {
    const { userId, jobId, resultId, status, data } = req.body;

    if (!userId || !jobId || !resultId) {
      return res.status(400).json({
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Missing required fields: userId, jobId, resultId' }
      });
    }

    console.log('Received analysis completion notification:', userId, jobId, resultId);

    const notificationData = {
      jobId,
      resultId,
      status: 'completed',
      message: 'Your personality analysis is ready!',
      data: data || {}
    };

    const result = sendNotificationToUser(userId, 'analysis-complete', notificationData);

    console.log('Analysis completion notification processed:', result);

    res.json({
      success: true,
      message: 'Notification sent successfully',
      data: { userId, jobId, resultId, delivered: result.delivered, connectedClients: result.connectedClients }
    });

  } catch (error) {
    console.error('Error processing analysis completion notification:', error.message);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to process notification' }
    });
  }
});

// Analysis failed notification
app.post('/notifications/analysis-failed', authenticateInternalService, (req, res) => {
  try {
    const { userId, jobId, status, error } = req.body;

    if (!userId || !jobId) {
      return res.status(400).json({
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Missing required fields: userId, jobId' }
      });
    }

    console.log('Received analysis failure notification:', userId, jobId);

    const notificationData = {
      jobId,
      status: 'failed',
      message: 'Analysis failed. Please try again.',
      error: error || { code: 'ANALYSIS_ERROR', message: 'An error occurred during analysis' }
    };

    const result = sendNotificationToUser(userId, 'analysis-failed', notificationData);

    console.log('Analysis failure notification processed:', result);

    res.json({
      success: true,
      message: 'Failure notification sent successfully',
      data: { userId, jobId, delivered: result.delivered, connectedClients: result.connectedClients }
    });

  } catch (error) {
    console.error('Error processing analysis failure notification:', error.message);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to process failure notification' }
    });
  }
});

// Connection status
app.get('/notifications/status', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Authorization token required' }
      });
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.id;

    const userSockets = userConnections.get(userId);
    const connected = userSockets && userSockets.size > 0;

    res.json({
      success: true,
      data: {
        userId,
        connected,
        connectionCount: connected ? userSockets.size : 0
      }
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: { code: 'INVALID_TOKEN', message: 'Invalid or expired token' }
      });
    }

    console.error('Error getting connection status:', error.message);
    res.status(500).json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Failed to get connection status' }
    });
  }
});

// Root endpoint
app.get('/', (req, res) => {
  const totalConnections = Array.from(userConnections.values()).reduce((total, sockets) => total + sockets.size, 0);
  
  res.json({
    success: true,
    message: 'ATMA Notification Service is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    websocket: {
      connectedUsers: userConnections.size,
      totalConnections
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: { code: 'NOT_FOUND', message: `Route ${req.method} ${req.originalUrl} not found` }
  });
});

// Start server
const serverInstance = server.listen(PORT, () => {
  console.log(`Notification Service started on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV}`);
  console.log(`WebSocket CORS: ${process.env.WEBSOCKET_CORS_ORIGIN}`);
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`${signal} received, shutting down gracefully`);
  
  serverInstance.close(() => {
    console.log('HTTP server closed');
    
    io.close(() => {
      console.log('WebSocket server closed');
      console.log('Process terminated');
      process.exit(0);
    });
  });
  
  setTimeout(() => {
    console.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error.message);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection:', reason);
  process.exit(1);
});

module.exports = { app, server, io };
