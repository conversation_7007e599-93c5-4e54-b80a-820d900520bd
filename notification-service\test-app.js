const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

console.log('Testing Socket.IO setup...');

const app = express();
const server = http.createServer(app);

// Basic middleware
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.json({ message: 'Socket.IO test working' });
});

// Socket.IO setup
console.log('Setting up Socket.IO...');
const io = socketIo(server, {
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST']
  }
});

console.log('Socket.IO configured');

// Basic WebSocket handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.emit('welcome', { message: 'Connected to test server' });
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 3005;

server.listen(PORT, () => {
  console.log(`Test server with Socket.IO running on port ${PORT}`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
  process.exit(1);
});
