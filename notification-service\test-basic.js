const express = require('express');
const http = require('http');

console.log('Testing basic Express setup...');

const app = express();
const server = http.createServer(app);

app.get('/', (req, res) => {
  res.json({ message: 'Basic test working' });
});

const PORT = 3005;

server.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  
  // Test the endpoint
  setTimeout(() => {
    const http = require('http');
    const options = {
      hostname: 'localhost',
      port: PORT,
      path: '/',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log('Response:', data);
        server.close();
        process.exit(0);
      });
    });

    req.on('error', (err) => {
      console.error('Request error:', err);
      server.close();
      process.exit(1);
    });

    req.end();
  }, 1000);
});

server.on('error', (err) => {
  console.error('Server error:', err);
  process.exit(1);
});
